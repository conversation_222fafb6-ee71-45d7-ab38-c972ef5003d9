.page[b-45rkf8v3vv] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-45rkf8v3vv] {
    flex: 1;
}

.sidebar[b-45rkf8v3vv] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-45rkf8v3vv] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-45rkf8v3vv]  a, .top-row .btn-link[b-45rkf8v3vv] {
        white-space: nowrap;
        margin-left: 1.5rem;
    }

    .top-row a:first-child[b-45rkf8v3vv] {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row:not(.auth)[b-45rkf8v3vv] {
        display: none;
    }

    .top-row.auth[b-45rkf8v3vv] {
        justify-content: space-between;
    }

    .top-row a[b-45rkf8v3vv], .top-row .btn-link[b-45rkf8v3vv] {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-45rkf8v3vv] {
        flex-direction: row;
    }

    .sidebar[b-45rkf8v3vv] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-45rkf8v3vv] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row[b-45rkf8v3vv], article[b-45rkf8v3vv] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
