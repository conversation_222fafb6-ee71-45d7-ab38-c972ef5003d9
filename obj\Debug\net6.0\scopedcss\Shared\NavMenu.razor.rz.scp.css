.navbar-toggler[b-b5wscdn8ts] {
    background-color: rgba(255, 255, 255, 0.1);
}

.top-row[b-b5wscdn8ts] {
    height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand[b-b5wscdn8ts] {
    font-size: 1.1rem;
}

.oi[b-b5wscdn8ts] {
    width: 2rem;
    font-size: 1.1rem;
    vertical-align: text-top;
    top: -2px;
}

.nav-item[b-b5wscdn8ts] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type[b-b5wscdn8ts] {
        padding-top: 1rem;
    }

    .nav-item:last-of-type[b-b5wscdn8ts] {
        padding-bottom: 1rem;
    }

    .nav-item[b-b5wscdn8ts]  a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

.nav-item[b-b5wscdn8ts]  a.active {
    background-color: rgba(255,255,255,0.25);
    color: white;
}

.nav-item[b-b5wscdn8ts]  a:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

@media (min-width: 641px) {
    .navbar-toggler[b-b5wscdn8ts] {
        display: none;
    }

    .collapse[b-b5wscdn8ts] {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }
}
