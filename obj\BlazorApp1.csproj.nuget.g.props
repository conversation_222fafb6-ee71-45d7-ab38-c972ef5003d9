﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages;C:\Program Files\dotnet\sdk\NuGetFallbackFolder</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.13.2</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
    <SourceRoot Include="C:\Program Files\dotnet\sdk\NuGetFallbackFolder\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.themes\27.1.51\buildTransitive\Syncfusion.Blazor.Themes.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.themes\27.1.51\buildTransitive\Syncfusion.Blazor.Themes.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.core\27.1.51\buildTransitive\Syncfusion.Blazor.Core.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.core\27.1.51\buildTransitive\Syncfusion.Blazor.Core.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.spinner\27.1.51\buildTransitive\Syncfusion.Blazor.Spinner.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.spinner\27.1.51\buildTransitive\Syncfusion.Blazor.Spinner.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.buttons\27.1.51\buildTransitive\Syncfusion.Blazor.Buttons.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.buttons\27.1.51\buildTransitive\Syncfusion.Blazor.Buttons.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.popups\27.1.51\buildTransitive\Syncfusion.Blazor.Popups.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.popups\27.1.51\buildTransitive\Syncfusion.Blazor.Popups.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.splitbuttons\27.1.51\buildTransitive\Syncfusion.Blazor.SplitButtons.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.splitbuttons\27.1.51\buildTransitive\Syncfusion.Blazor.SplitButtons.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.notifications\27.1.51\buildTransitive\Syncfusion.Blazor.Notifications.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.notifications\27.1.51\buildTransitive\Syncfusion.Blazor.Notifications.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.data\27.1.51\buildTransitive\Syncfusion.Blazor.Data.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.data\27.1.51\buildTransitive\Syncfusion.Blazor.Data.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.lists\27.1.51\buildTransitive\Syncfusion.Blazor.Lists.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.lists\27.1.51\buildTransitive\Syncfusion.Blazor.Lists.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.inputs\27.1.51\buildTransitive\Syncfusion.Blazor.Inputs.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.inputs\27.1.51\buildTransitive\Syncfusion.Blazor.Inputs.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.dropdowns\27.1.51\buildTransitive\Syncfusion.Blazor.DropDowns.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.dropdowns\27.1.51\buildTransitive\Syncfusion.Blazor.DropDowns.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.navigations\27.1.51\buildTransitive\Syncfusion.Blazor.Navigations.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.navigations\27.1.51\buildTransitive\Syncfusion.Blazor.Navigations.props')" />
  </ImportGroup>
</Project>