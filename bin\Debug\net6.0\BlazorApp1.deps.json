{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"BlazorApp1/1.0.0": {"dependencies": {"Syncfusion.Blazor.Inputs": "27.1.51", "Syncfusion.Blazor.Navigations": "27.1.51", "Syncfusion.Blazor.Themes": "27.1.51"}, "runtime": {"BlazorApp1.dll": {}}}, "Microsoft.AspNetCore.Authorization/6.0.5": {"dependencies": {"Microsoft.AspNetCore.Metadata": "6.0.5", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21802"}}}, "Microsoft.AspNetCore.Components/6.0.5": {"dependencies": {"Microsoft.AspNetCore.Authorization": "6.0.5", "Microsoft.AspNetCore.Components.Analyzers": "6.0.5"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21802"}}}, "Microsoft.AspNetCore.Components.Analyzers/6.0.5": {}, "Microsoft.AspNetCore.Components.Forms/6.0.5": {"dependencies": {"Microsoft.AspNetCore.Components": "6.0.5"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21802"}}}, "Microsoft.AspNetCore.Components.Web/6.0.5": {"dependencies": {"Microsoft.AspNetCore.Components": "6.0.5", "Microsoft.AspNetCore.Components.Forms": "6.0.5", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.JSInterop": "6.0.5", "System.IO.Pipelines": "6.0.3"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21802"}}}, "Microsoft.AspNetCore.Metadata/6.0.5": {"runtime": {"lib/net6.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21802"}}}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.Logging.Abstractions/6.0.1": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.322.12309"}}}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.JSInterop/6.0.5": {"runtime": {"lib/net6.0/Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21802"}}}, "Syncfusion.Blazor.Buttons/27.1.51": {"dependencies": {"Syncfusion.Blazor.Core": "27.1.51"}, "runtime": {"lib/net6.0/Syncfusion.Blazor.Buttons.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Core/27.1.51": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "6.0.5", "Syncfusion.Licensing": "27.1.51", "System.Text.Json": "6.0.9"}, "runtime": {"lib/net6.0/Syncfusion.Blazor.Core.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Data/27.1.51": {"dependencies": {"Syncfusion.Blazor.Core": "27.1.51"}, "runtime": {"lib/net6.0/Syncfusion.Blazor.Data.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.DropDowns/27.1.51": {"dependencies": {"Syncfusion.Blazor.Buttons": "27.1.51", "Syncfusion.Blazor.Core": "27.1.51", "Syncfusion.Blazor.Data": "27.1.51", "Syncfusion.Blazor.Inputs": "27.1.51", "Syncfusion.Blazor.Notifications": "27.1.51", "Syncfusion.Blazor.Spinner": "27.1.51"}, "runtime": {"lib/net6.0/Syncfusion.Blazor.DropDowns.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Inputs/27.1.51": {"dependencies": {"Syncfusion.Blazor.Core": "27.1.51", "Syncfusion.Blazor.Data": "27.1.51", "Syncfusion.Blazor.Popups": "27.1.51", "Syncfusion.Blazor.Spinner": "27.1.51", "Syncfusion.Blazor.SplitButtons": "27.1.51"}, "runtime": {"lib/net6.0/Syncfusion.Blazor.Inputs.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Lists/27.1.51": {"dependencies": {"Syncfusion.Blazor.Buttons": "27.1.51", "Syncfusion.Blazor.Core": "27.1.51", "Syncfusion.Blazor.Data": "27.1.51"}, "runtime": {"lib/net6.0/Syncfusion.Blazor.Lists.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Navigations/27.1.51": {"dependencies": {"Syncfusion.Blazor.Buttons": "27.1.51", "Syncfusion.Blazor.Core": "27.1.51", "Syncfusion.Blazor.Data": "27.1.51", "Syncfusion.Blazor.DropDowns": "27.1.51", "Syncfusion.Blazor.Inputs": "27.1.51", "Syncfusion.Blazor.Lists": "27.1.51", "Syncfusion.Blazor.Popups": "27.1.51", "Syncfusion.Blazor.Spinner": "27.1.51"}, "runtime": {"lib/net6.0/Syncfusion.Blazor.Navigations.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Notifications/27.1.51": {"dependencies": {"Syncfusion.Blazor.Buttons": "27.1.51", "Syncfusion.Blazor.Core": "27.1.51"}, "runtime": {"lib/net6.0/Syncfusion.Blazor.Notifications.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Popups/27.1.51": {"dependencies": {"Syncfusion.Blazor.Buttons": "27.1.51", "Syncfusion.Blazor.Core": "27.1.51"}, "runtime": {"lib/net6.0/Syncfusion.Blazor.Popups.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Spinner/27.1.51": {"dependencies": {"Syncfusion.Blazor.Core": "27.1.51"}, "runtime": {"lib/net6.0/Syncfusion.Blazor.Spinner.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.SplitButtons/27.1.51": {"dependencies": {"Syncfusion.Blazor.Buttons": "27.1.51", "Syncfusion.Blazor.Core": "27.1.51", "Syncfusion.Blazor.Popups": "27.1.51", "Syncfusion.Blazor.Spinner": "27.1.51"}, "runtime": {"lib/net6.0/Syncfusion.Blazor.SplitButtons.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Themes/27.1.51": {"runtime": {"lib/net6.0/Syncfusion.Blazor.Themes.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Licensing/27.1.51": {"runtime": {"lib/net6.0/Syncfusion.Licensing.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "System.IO.Pipelines/6.0.3": {"runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21309"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.9": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.2523.51912"}}}}}, "libraries": {"BlazorApp1/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-T3J4Z4xK/mmJlMv/j7Cs1PZjRLh1fGsvHay3RNBNooP0qhHCk3UnU+WgBvcGApZFnEfeyOEGZBHZv5gb1RrDLA==", "path": "microsoft.aspnetcore.authorization/6.0.5", "hashPath": "microsoft.aspnetcore.authorization.6.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Components/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pm7wo05ANEduCj/HCKVIvBLL8BzszHttgHtX/hbV5f6xb6/0oCsTuZexRmBfM30PrlbQDjzWuH1rpiPYP30l7g==", "path": "microsoft.aspnetcore.components/6.0.5", "hashPath": "microsoft.aspnetcore.components.6.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-zxAXJIA/VL3BkqBPvgNiojppfmhmOIB8JtWi1Q85kcy1MGxwOi+UXAFXlvaMliocrycUazTguZBTXMhC2jFiow==", "path": "microsoft.aspnetcore.components.analyzers/6.0.5", "hashPath": "microsoft.aspnetcore.components.analyzers.6.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-yh8bWPxPkJ/60IHVA6Vv5dPuyG/i4LiG6Nwkq/8ejtHn/hyGuPBrcsymBjLGQEqSmhTPnqteIL9BOP7VBYV1NQ==", "path": "microsoft.aspnetcore.components.forms/6.0.5", "hashPath": "microsoft.aspnetcore.components.forms.6.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-QbPAY7trw1IpafCIbizib1IW/3Lqxf3AJ6tCO/FzbmvWeImD3rNLrgWb3gH7Mz+RtkmYmbrfdEk4eh9mW/2tGg==", "path": "microsoft.aspnetcore.components.web/6.0.5", "hashPath": "microsoft.aspnetcore.components.web.6.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-4VRJoJa/EbncxQTAzM6abqQUUbNrohg5MC1glZ1R1lzzXJM2CdXiRKvcpAfn3luAwPzkwJtHejuLgI6Osn0aDA==", "path": "microsoft.aspnetcore.metadata/6.0.5", "hashPath": "microsoft.aspnetcore.metadata.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k6PWQMuoBDGGHOQTtyois2u4AwyVcIwL2LaSLlTZQm2CYcJ1pxbt6jfAnpWmzENA/wfrYRI/X9DTLoUkE4AsLw==", "path": "microsoft.extensions.dependencyinjection/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dzB2Cgg+JmrouhjkcQGzSFjjvpwlq353i8oBQO2GWNjCXSzhbtBRUf28HSauWe7eib3wYOdb3tItdjRwAdwCSg==", "path": "microsoft.extensions.logging.abstractions/6.0.1", "hashPath": "microsoft.extensions.logging.abstractions.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.JSInterop/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ST48YNjqpW2WNbTFBN5V3fdpnV8Civ6VeewzGtKdtYtBtM0q5afQVnUOwx3w5oeBZNQjn2nk3OZM1tSe10x4lA==", "path": "microsoft.jsinterop/6.0.5", "hashPath": "microsoft.jsinterop.6.0.5.nupkg.sha512"}, "Syncfusion.Blazor.Buttons/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-vdgbzoUEOPwxL5lrAuOInG9EfUMGIaPXtYIEca1yRb+wtVmItDXPjMYu1ARCbF90aYbqwb7c/XALBNAxKSM+Dg==", "path": "syncfusion.blazor.buttons/27.1.51", "hashPath": "syncfusion.blazor.buttons.27.1.51.nupkg.sha512"}, "Syncfusion.Blazor.Core/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-tZWisXX5dXEy2U5lr054VpBb9j30pU2BAPrEbpuSryrhbnjduK31DIbZBflOFORhXyps7nwvt5RyM5vlBo3BhA==", "path": "syncfusion.blazor.core/27.1.51", "hashPath": "syncfusion.blazor.core.27.1.51.nupkg.sha512"}, "Syncfusion.Blazor.Data/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-67dUaBg9TqixJbZ32ORnPd/0GHC5rkfqSH2BbiLf+s15C+CRg35U9cNEgi/Xgbp7U4r/mkKbOy5OVzvWVuJjNg==", "path": "syncfusion.blazor.data/27.1.51", "hashPath": "syncfusion.blazor.data.27.1.51.nupkg.sha512"}, "Syncfusion.Blazor.DropDowns/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-jwfoFXNlZBfxZqsJXmsz73QKjpPqsUbAco6ELvmsjIHPsT5hgOCj/w146Dzneu63FAHIgmTgcl/D8NXgROkiZw==", "path": "syncfusion.blazor.dropdowns/27.1.51", "hashPath": "syncfusion.blazor.dropdowns.27.1.51.nupkg.sha512"}, "Syncfusion.Blazor.Inputs/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-jdPg9D4/f4I2Pd+cXBAhyNGgP5moPPLf7ujqof8j21Y+c5q+CsyLWu17Lkh1+JrnoA94Etq8pKZIgnekfHJOtA==", "path": "syncfusion.blazor.inputs/27.1.51", "hashPath": "syncfusion.blazor.inputs.27.1.51.nupkg.sha512"}, "Syncfusion.Blazor.Lists/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-k1UiZBnpSn+hZqSD/Ht+DflnkFM1pnuPbT98kRKwwz7BaVPpG0m3WIw/u+QIZsVzTcp2KSml3eLqkJm1R91KiQ==", "path": "syncfusion.blazor.lists/27.1.51", "hashPath": "syncfusion.blazor.lists.27.1.51.nupkg.sha512"}, "Syncfusion.Blazor.Navigations/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-5VzlRHyIGkg4C8pan5AQs6tPzNqOdqAr9ADcNfHDufG3l4EvRWkf5wCRNdGDU4jPtPuzSy7BGq840hoWvlDDJQ==", "path": "syncfusion.blazor.navigations/27.1.51", "hashPath": "syncfusion.blazor.navigations.27.1.51.nupkg.sha512"}, "Syncfusion.Blazor.Notifications/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-9KPXlS/fYrSWydWQUK12C+Xfx7NvVbWVCOwSnehxvON3+wssUBkPVmGUWMBwCm6i2kZhZEwh6MGMiXkBpacjRA==", "path": "syncfusion.blazor.notifications/27.1.51", "hashPath": "syncfusion.blazor.notifications.27.1.51.nupkg.sha512"}, "Syncfusion.Blazor.Popups/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-fovMciCbbEHz8huU/GlXVzuaKLC9eWaw/G+ZMksiohaiwMz2a61QZ089rGY5A+VvGWXE5BLlpMOa0MVBd9i82w==", "path": "syncfusion.blazor.popups/27.1.51", "hashPath": "syncfusion.blazor.popups.27.1.51.nupkg.sha512"}, "Syncfusion.Blazor.Spinner/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-sSEQALWC+QJWr1t7H1lHKLN/wjstgJmoAkHhtBT6V8NWor6AcR3gsChTsF+7v0qhJzwbjnEydGcLQujXA8EFzg==", "path": "syncfusion.blazor.spinner/27.1.51", "hashPath": "syncfusion.blazor.spinner.27.1.51.nupkg.sha512"}, "Syncfusion.Blazor.SplitButtons/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-Cj/Eg4cyQFBEoOEK7BPdFIyhiFv0SUYXQC1kNoL4A7VG5D7agUiJIEsw4d58mexZdxw5iQOimKDYo3YHwoH7hQ==", "path": "syncfusion.blazor.splitbuttons/27.1.51", "hashPath": "syncfusion.blazor.splitbuttons.27.1.51.nupkg.sha512"}, "Syncfusion.Blazor.Themes/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-pb5ttqd/9NGPrz+USCsBhcghdW2nQU/mUWIO3YhB0qLK/X92vGt12qaGAE2oxO6ipNFQQaJ8g8W496y9HfIJkA==", "path": "syncfusion.blazor.themes/27.1.51", "hashPath": "syncfusion.blazor.themes.27.1.51.nupkg.sha512"}, "Syncfusion.Licensing/27.1.51": {"type": "package", "serviceable": true, "sha512": "sha512-XFPmIoWwCe491C2yIGeeeOmyllgP/5rVvpeiBn2onqgsovrdZDSqKgizPjntlBRQSWH0JZTn6r0dfmK0JckD2A==", "path": "syncfusion.licensing/27.1.51", "hashPath": "syncfusion.licensing.27.1.51.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-2j16oUgtIzl7Xtk7demG0i/v5aU/ZvULcAnJvPb63U3ZhXJ494UYcxuEj5Fs49i3XDrk5kU/8I+6l9zRCw3cJw==", "path": "system.text.json/6.0.9", "hashPath": "system.text.json.6.0.9.nupkg.sha512"}}}