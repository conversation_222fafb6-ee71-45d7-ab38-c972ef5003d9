{"version": 2, "dgSpecHash": "iNeSw0um6wU=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\BlazorApp1.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\6.0.5\\microsoft.aspnetcore.authorization.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components\\6.0.5\\microsoft.aspnetcore.components.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.analyzers\\6.0.5\\microsoft.aspnetcore.components.analyzers.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.forms\\6.0.5\\microsoft.aspnetcore.components.forms.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.web\\6.0.5\\microsoft.aspnetcore.components.web.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\6.0.5\\microsoft.aspnetcore.metadata.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\6.0.0\\microsoft.extensions.dependencyinjection.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\6.0.0\\microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\6.0.1\\microsoft.extensions.logging.abstractions.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\6.0.0\\microsoft.extensions.options.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\6.0.0\\microsoft.extensions.primitives.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop\\6.0.5\\microsoft.jsinterop.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\27.1.51\\syncfusion.blazor.buttons.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\27.1.51\\syncfusion.blazor.core.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\27.1.51\\syncfusion.blazor.data.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\27.1.51\\syncfusion.blazor.dropdowns.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\27.1.51\\syncfusion.blazor.inputs.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\27.1.51\\syncfusion.blazor.lists.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\27.1.51\\syncfusion.blazor.navigations.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\27.1.51\\syncfusion.blazor.notifications.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\27.1.51\\syncfusion.blazor.popups.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\27.1.51\\syncfusion.blazor.spinner.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\27.1.51\\syncfusion.blazor.splitbuttons.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\27.1.51\\syncfusion.blazor.themes.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.licensing\\27.1.51\\syncfusion.licensing.27.1.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\6.0.3\\system.io.pipelines.6.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\6.0.9\\system.text.json.6.0.9.nupkg.sha512"], "logs": []}