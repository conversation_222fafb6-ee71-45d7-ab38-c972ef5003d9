{"Files": [{"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\obj\\Debug\\net6.0\\scopedcss\\projectbundle\\BlazorApp1.bundle.scp.css", "PackagePath": "staticwebassets\\BlazorApp1.1qoeyp8pam.bundle.scp.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "PackagePath": "staticwebassets\\css\\bootstrap\\bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\css\\bootstrap\\bootstrap.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "PackagePath": "staticwebassets\\css\\open-iconic"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "PackagePath": "staticwebassets\\css\\open-iconic"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\open-iconic\\README.md", "PackagePath": "staticwebassets\\css\\open-iconic\\README.md"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\fonts\\open-iconic.eot"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\fonts\\open-iconic.otf"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\fonts\\open-iconic.svg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\fonts\\open-iconic.ttf"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\fonts\\open-iconic.woff"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\BlazorSidebarMediaQuery_28ddb6cb\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.BlazorApp1.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.BlazorApp1.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.build.BlazorApp1.props", "PackagePath": "build\\BlazorApp1.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildMultiTargeting.BlazorApp1.props", "PackagePath": "buildMultiTargeting\\BlazorApp1.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildTransitive.BlazorApp1.props", "PackagePath": "buildTransitive\\BlazorApp1.props"}], "ElementsToRemove": []}