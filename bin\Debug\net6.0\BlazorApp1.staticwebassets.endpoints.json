{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "BlazorApp1.1qoeyp8pam.styles.css", "AssetFile": "BlazorApp1.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2841"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qZq69ckbP8N1UWX8l1of8bVVoSVQjwX0Wt5WdA/6vAY=\""}, {"Name": "Last-Modified", "Value": "Thu, 26 Jun 2025 09:44:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1qoeyp8pam"}, {"Name": "integrity", "Value": "sha256-qZq69ckbP8N1UWX8l1of8bVVoSVQjwX0Wt5WdA/6vAY="}, {"Name": "label", "Value": "BlazorApp1.styles.css"}]}, {"Route": "BlazorApp1.styles.css", "AssetFile": "BlazorApp1.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2841"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qZq69ckbP8N1UWX8l1of8bVVoSVQjwX0Wt5WdA/6vAY=\""}, {"Name": "Last-Modified", "Value": "Thu, 26 Jun 2025 09:44:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qZq69ckbP8N1UWX8l1of8bVVoSVQjwX0Wt5WdA/6vAY="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1913"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QXbqC/poRfQQHt9bgYx55Vi3wBzQ9xr2MjTkyS6XErs=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QXbqC/poRfQQHt9bgYx55Vi3wBzQ9xr2MjTkyS6XErs="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11692"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mhKaVgdChY2kfQ3uzIpRTzvtEsHzBF+ZoSR+lePyGq0=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mhKaVgdChY2kfQ3uzIpRTzvtEsHzBF+ZoSR+lePyGq0="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16398"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DHcA4QwwUGwAwpuzp9IUhvyqWE0tnwzhRhS9XiB6dFs=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DHcA4QwwUGwAwpuzp9IUhvyqWE0tnwzhRhS9XiB6dFs="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13752"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XUUDdSyfuCJNHkZhmqu4pTpsmoBKW09B/oUCnlQ6S+o=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XUUDdSyfuCJNHkZhmqu4pTpsmoBKW09B/oUCnlQ6S+o="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13860"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UDIW4I0kxcZRF7jOT4WBYSLVkv6/a2wsKrRgvTF6Eco=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UDIW4I0kxcZRF7jOT4WBYSLVkv6/a2wsKrRgvTF6Eco="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "52091"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N96YQjBLEkSC3vVTPf+bEPoR049VYlHr9aVI4zSCY1c=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N96YQjBLEkSC3vVTPf+bEPoR049VYlHr9aVI4zSCY1c="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "236492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LUcpWjxMSZrz0pE6rlzAVRjSWoNM/hhxXb2Tc8JnNgQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LUcpWjxMSZrz0pE6rlzAVRjSWoNM/hhxXb2Tc8JnNgQ="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4171952"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xsW1dB0vmTbRA2sxPfVFwHS543Ipa6oUshWw6pMR9Ys=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xsW1dB0vmTbRA2sxPfVFwHS543Ipa6oUshWw6pMR9Ys="}]}, {"Route": "_content/Syncfusion.Blazor.Data/scripts/data.min.js", "AssetFile": "_content/Syncfusion.Blazor.Data/scripts/data.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "84953"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KKrxCUmeDhNYLLBnWzZI+2YPAtrbTOpsLvzpWD4rZjY=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KKrxCUmeDhNYLLBnWzZI+2YPAtrbTOpsLvzpWD4rZjY="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "36831"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FcVFxlhVfOKci7HnZfdTApQkd+LJE+/ioVkbwgnw40s=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FcVFxlhVfOKci7HnZfdTApQkd+LJE+/ioVkbwgnw40s="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5503"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"R8BMTWPrKa9IDuz9fecaAqvXoP/ARCYMKySdIfUp4pU=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R8BMTWPrKa9IDuz9fecaAqvXoP/ARCYMKySdIfUp4pU="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19973"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"veTBZXXt8HpbBb/5wRWyBrRzK3PwSOPwkQotvJMMgbY=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-veTBZXXt8HpbBb/5wRWyBrRzK3PwSOPwkQotvJMMgbY="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32551"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"I2pimOeKEN10g51Vn/YMfKAkwnwXMeySR4BXAF4yNEo=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-I2pimOeKEN10g51Vn/YMfKAkwnwXMeySR4BXAF4yNEo="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10634"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1AMhm6y/WEVRJ6P3r70Uz7ZYRlwJkn51FncmF29HSeg=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1AMhm6y/WEVRJ6P3r70Uz7ZYRlwJkn51FncmF29HSeg="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6110"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ScEuAn556o7/55aGaGUNBeS0YvgsBPNF0TtjR8VuTSY=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ScEuAn556o7/55aGaGUNBeS0YvgsBPNF0TtjR8VuTSY="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8944"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qb4W0XL11AMEkkTbVhkve8O/LbQ6hX0WKUj+9HeV6hI=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qb4W0XL11AMEkkTbVhkve8O/LbQ6hX0WKUj+9HeV6hI="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12899"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"I/o4gkB3UiBU2ZhX66JcKoFGjiNmLQMr2TtRUPK4lLo=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-I/o4gkB3UiBU2ZhX66JcKoFGjiNmLQMr2TtRUPK4lLo="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1996"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"F6gKdQrkdHh/ExEbXoGBCiN4Z4JCg8A/alZXUrdhV/k=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F6gKdQrkdHh/ExEbXoGBCiN4Z4JCg8A/alZXUrdhV/k="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9161"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"420D+tLoen+UJO8niWhBarpg+Oy9RAnad0kbHlmiAU0=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-420D+tLoen+UJO8niWhBarpg+Oy9RAnad0kbHlmiAU0="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23686"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tP9RY04nw+LRlLHcenWb1fN9ZWVGSR+0JiEyQ1lKVkU=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tP9RY04nw+LRlLHcenWb1fN9ZWVGSR+0JiEyQ1lKVkU="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "31650"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fy136haCJkc4zUmN7nMNGspas9jVWOJigPuqSoFw8GY=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fy136haCJkc4zUmN7nMNGspas9jVWOJigPuqSoFw8GY="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7bHbpZE9zsh9NWHBqs6dmbv/PdPMDk25oH4HCbJnSCE=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7bHbpZE9zsh9NWHBqs6dmbv/PdPMDk25oH4HCbJnSCE="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2646"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0Ro4RNc7wmSUhT3i1raaZn90WhyLOfBvGuG5rQ3bvtc=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0Ro4RNc7wmSUhT3i1raaZn90WhyLOfBvGuG5rQ3bvtc="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "82934"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"t/FuVq/tYdEKNy9dCSihyNjZFKBs1un83olfumbRxuo=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-t/FuVq/tYdEKNy9dCSihyNjZFKBs1un83olfumbRxuo="}]}, {"Route": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js", "AssetFile": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23615"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+u+35MCrSugVJGJKTtjYmSstqs5rs7VKLbzWqdcK3iA=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+u+35MCrSugVJGJKTtjYmSstqs5rs7VKLbzWqdcK3iA="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19975"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ym8Cz36tahXEZvYx+keOEDHtwBg5pGeMZc3xG+/VgL4=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ym8Cz36tahXEZvYx+keOEDHtwBg5pGeMZc3xG+/VgL4="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12260"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PTsANqNS0FGuWK8iO9C5UDobCLndZ1ewZlnlOX9RHsE=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PTsANqNS0FGuWK8iO9C5UDobCLndZ1ewZlnlOX9RHsE="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4636"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2RWTFv2yLuGvI/Sljjc/Y5VS8bPFJCgdWD6nVsVKI6k=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2RWTFv2yLuGvI/Sljjc/Y5VS8bPFJCgdWD6nVsVKI6k="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5539"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6fl2J/zs2lpd4tUhkzTLTlqPiNf/IDIUL90Z5GCRo7s=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6fl2J/zs2lpd4tUhkzTLTlqPiNf/IDIUL90Z5GCRo7s="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16914"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8zbUsrrr5sqOwJd0LRFU9Pd8VAgw4yFotyyLy+OKQNg=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8zbUsrrr5sqOwJd0LRFU9Pd8VAgw4yFotyyLy+OKQNg="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "25784"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"McsZWOlV6G/VfYNd6aQz94D5C+Pfs6SllM/wY3IfbXQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-McsZWOlV6G/VfYNd6aQz94D5C+Pfs6SllM/wY3IfbXQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13654"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GDcyb2I9AUp07oF2KoV3oDA2nblaA5De9/t+Cg2by14=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GDcyb2I9AUp07oF2KoV3oDA2nblaA5De9/t+Cg2by14="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9652"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TLnT+ADb0XkkbMGpgrVdTgEq7gfTBoj9+rH+niBw9Sw=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TLnT+ADb0XkkbMGpgrVdTgEq7gfTBoj9+rH+niBw9Sw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10300"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ca8alupgFdVJ6OgiW1MnfS+Qg9P20Ycd4Aoau70I3G4=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ca8alupgFdVJ6OgiW1MnfS+Qg9P20Ycd4Aoau70I3G4="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14925"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XM4SoiwxbR94oc1RPG8kLqS73pJ8xABOmpnC93wVA8w=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XM4SoiwxbR94oc1RPG8kLqS73pJ8xABOmpnC93wVA8w="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "33579"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vwRABFZol/fdotv7Pxelka0S4r/3IYfKVy2En5DLqE4=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vwRABFZol/fdotv7Pxelka0S4r/3IYfKVy2En5DLqE4="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41114"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dEObVjVQoFeHq5vnydNXjavHHSxzyy5oUgqIzzJlLvE=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dEObVjVQoFeHq5vnydNXjavHHSxzyy5oUgqIzzJlLvE="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "49749"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IA8nOK0dj4p3uIaTQ6azaLWt23HSbd5/+FxLIvT8ea4=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IA8nOK0dj4p3uIaTQ6azaLWt23HSbd5/+FxLIvT8ea4="}]}, {"Route": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js", "AssetFile": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7161"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0vnz+PdqD13uFF9UEq/A3FjlY38Wr8VHPoo/Pxu049I=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0vnz+PdqD13uFF9UEq/A3FjlY38Wr8VHPoo/Pxu049I="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22764"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"R8XtOmsoVi3PI07+EM7hB9I5znLYa6mYd/lLasnKkv4=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R8XtOmsoVi3PI07+EM7hB9I5znLYa6mYd/lLasnKkv4="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30341"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"o2RTu+ZsGh3+YEH8LC7JyRjgtd9m57XmIbo5WCovYJI=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o2RTu+ZsGh3+YEH8LC7JyRjgtd9m57XmIbo5WCovYJI="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/0afG60Hj8XeCufYwc9JxJ+S+u8HMZMqbLltgB6xs3Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/0afG60Hj8XeCufYwc9JxJ+S+u8HMZMqbLltgB6xs3Q="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10880"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CfrdMNmwxecQleTpxacPh5EIfN52wnn/CAHWWDPck4I=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CfrdMNmwxecQleTpxacPh5EIfN52wnn/CAHWWDPck4I="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7881"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nDNCE/AGn5vYofhFE7uUidbcyqwEEj4z6n0m+17lsTw=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nDNCE/AGn5vYofhFE7uUidbcyqwEEj4z6n0m+17lsTw="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3674"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eLFKlRGJNsbTpPUOPF/EsHvq/YrEOO8e8pxr2Htmhr0=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 01:00:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eLFKlRGJNsbTpPUOPF/EsHvq/YrEOO8e8pxr2Htmhr0="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bds-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bds-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2750180"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bEvjXO7Zj1kPBg2dkI5kmdySrLu5eE44is9qQVJ6X2M=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bEvjXO7Zj1kPBg2dkI5kmdySrLu5eE44is9qQVJ6X2M="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bds-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bds-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3573677"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pchhYMFLahnBDOoq6SdtzBwyy29uaU09y2yyKi+b2es=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pchhYMFLahnBDOoq6SdtzBwyy29uaU09y2yyKi+b2es="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bds-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bds-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2741996"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pzvLANIKgKY5hVvUfbNDG+TFDboSvHjQGurh9tC1nMU=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pzvLANIKgKY5hVvUfbNDG+TFDboSvHjQGurh9tC1nMU="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bds.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bds.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3566138"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3JiRQKIwyuziuTwL57yp/uRQYWBFXjipPVHPpFxjRX8=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3JiRQKIwyuziuTwL57yp/uRQYWBFXjipPVHPpFxjRX8="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2605221"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wb7ytkI4AhwCGsTq36DVsa1JM8VaXdjIBD7v2FZQWNw=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wb7ytkI4AhwCGsTq36DVsa1JM8VaXdjIBD7v2FZQWNw="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3382894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LoYB+aDyBgVzcPp/+gIgNi2/9mXLoYUKu1SFYISF2eE=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LoYB+aDyBgVzcPp/+gIgNi2/9mXLoYUKu1SFYISF2eE="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2588520"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zEY8hzF4jjUID/t6g9JmlOGPQOPgT55nYU4j8o9CPC4=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zEY8hzF4jjUID/t6g9JmlOGPQOPgT55nYU4j8o9CPC4="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3366433"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kDESAJSWPGaHsXd6qb0wPoCIPV16KjAE825zzQyqeyc=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kDESAJSWPGaHsXd6qb0wPoCIPV16KjAE825zzQyqeyc="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap4-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap4-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2616920"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EYNf9p8rq6/Z70ZOASsMf+A0IwpAQfUYQ5DvNLFXlM4=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EYNf9p8rq6/Z70ZOASsMf+A0IwpAQfUYQ5DvNLFXlM4="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap4.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap4.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3424688"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NAfaBloeOoAeUCKCpq7fk38KoN+21KH6oh8krGZc2XU=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NAfaBloeOoAeUCKCpq7fk38KoN+21KH6oh8krGZc2XU="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2632448"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3HvwvPnGc1GEgvKFWwgQSBI5Qibs09/THFpvJGpbifs=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3HvwvPnGc1GEgvKFWwgQSBI5Qibs09/THFpvJGpbifs="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3439249"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iGX+zT9+NvdxhKWs2hWiC4RvprC7V/dt1Xi13Jf1voo=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iGX+zT9+NvdxhKWs2hWiC4RvprC7V/dt1Xi13Jf1voo="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2632948"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rkgE6bMRARTaqBjDQTgrF0z1b2Pmq4APt12Z+a1rNpk=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rkgE6bMRARTaqBjDQTgrF0z1b2Pmq4APt12Z+a1rNpk="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2775916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"69NiT3KM+wgIVKHlmuCXD9gGn8jfQlDn2fU0dOqBuKg=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-69NiT3KM+wgIVKHlmuCXD9gGn8jfQlDn2fU0dOqBuKg="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3590786"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"r7eHBLY3jTcn1c9SQe9IWS/IqNNiGUwXrdy67Cmgj6g=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r7eHBLY3jTcn1c9SQe9IWS/IqNNiGUwXrdy67Cmgj6g="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2791571"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1SyV3nrjSPC02w4V3FpS8+6ojGjoF8s78hHiJRCODS4=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1SyV3nrjSPC02w4V3FpS8+6ojGjoF8s78hHiJRCODS4="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3606441"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hxVSwXCCdQcLjRjbNEhDfqnM6uq/mrIkXnTDwoR94B0=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hxVSwXCCdQcLjRjbNEhDfqnM6uq/mrIkXnTDwoR94B0="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3439713"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"f/j18ZLihQLsEHZ61XOmfgmch62YZjLsAued3bDrzJg=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f/j18ZLihQLsEHZ61XOmfgmch62YZjLsAued3bDrzJg="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/material-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/material-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4012286"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T4Oe6ozPGmo8dkCQFVh/vsLVk0+QxjwefDV73jLyUAw=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T4Oe6ozPGmo8dkCQFVh/vsLVk0+QxjwefDV73jLyUAw="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/material.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/material.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3967077"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H5ieG8VBwg6C1RoEJ0chGyTy0gmvWpH+S/dN3Ii1sHo=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H5ieG8VBwg6C1RoEJ0chGyTy0gmvWpH+S/dN3Ii1sHo="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/tailwind-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/tailwind-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3369142"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TG1zdyAqgyzUt6HZ4NUmaORHWjZj+CthmyN65IZOqp0=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TG1zdyAqgyzUt6HZ4NUmaORHWjZj+CthmyN65IZOqp0="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/tailwind.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3336824"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ThrEEBUuRUKBHkD3usIqYB067p+0gGYrUw3VQYzXwQ4=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ThrEEBUuRUKBHkD3usIqYB067p+0gGYrUw3VQYzXwQ4="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2548742"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UfCMxsTsbIcHsbYYeaNkW2l3pzTpAcEj8pBZC+zjN70=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UfCMxsTsbIcHsbYYeaNkW2l3pzTpAcEj8pBZC+zjN70="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3326419"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XBCL9kRJjDa8yghKKnxhqenVFsAwTnBGzHD8xRPlKAo=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XBCL9kRJjDa8yghKKnxhqenVFsAwTnBGzHD8xRPlKAo="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2494510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lxtqg952EpRK3US+Y6bXYBTFk1eH1Dkqenao+0c8MQQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lxtqg952EpRK3US+Y6bXYBTFk1eH1Dkqenao+0c8MQQ="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3273104"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QsFs73I9CovCZGP9aAGw6pO1txwNcHJMs6s5w3Rmnko=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QsFs73I9CovCZGP9aAGw6pO1txwNcHJMs6s5w3Rmnko="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2572564"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/JY0QHPchiB0Vr/zGWzAbzFcc8Y0ei5jiGN8dZWireQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/JY0QHPchiB0Vr/zGWzAbzFcc8Y0ei5jiGN8dZWireQ="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3383936"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fpU2y4VLG5t06MRYhlh/IeqjY5EH1UJxuQ5arTHhFJE=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fpU2y4VLG5t06MRYhlh/IeqjY5EH1UJxuQ5arTHhFJE="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2570759"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"riJ3UWfVvl8zVWTz8emOy3ZUt38F7LLBNEKD9fWUhwM=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-riJ3UWfVvl8zVWTz8emOy3ZUt38F7LLBNEKD9fWUhwM="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3381972"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ERH6/KdsoPA216OLM6tpZBBZ19IC0AH5YDi7kiWbBz4=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ERH6/KdsoPA216OLM6tpZBBZ19IC0AH5YDi7kiWbBz4="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2924958"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"WczNqKFa69YBVp46hKmCWE2fr13BOY0I4qeR91wlVEc=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WczNqKFa69YBVp46hKmCWE2fr13BOY0I4qeR91wlVEc="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3931951"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4m227gvaOh/MU416hUkC++Ohkhv4TKdh1qhGdNZfeSU=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4m227gvaOh/MU416hUkC++Ohkhv4TKdh1qhGdNZfeSU="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2962234"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9immOzeSn4CRvjzN+Ob5lJihmcGr+YKIKO8yLl7SvGg=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9immOzeSn4CRvjzN+Ob5lJihmcGr+YKIKO8yLl7SvGg="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3969227"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AiY9IMqgNSRd1B1j+Y2XvbyMfwH6PDuYLLJOefmSiZg=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AiY9IMqgNSRd1B1j+Y2XvbyMfwH6PDuYLLJOefmSiZg="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2963828"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iULz9O8I2q5BL0tlwKP20jSuVgP6k5fW18cra7utBMY=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iULz9O8I2q5BL0tlwKP20jSuVgP6k5fW18cra7utBMY="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3972335"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7tw/4+7bg8dU24ZKo914VWKm3KJo7fOn7nn8oFpTAdQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7tw/4+7bg8dU24ZKo914VWKm3KJo7fOn7nn8oFpTAdQ="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/highcontrast-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/highcontrast-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2506798"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"05LmJ/buEHqJMOLChejKH8dl5Gub2IwUx1hqFr6bR0s=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-05LmJ/buEHqJMOLChejKH8dl5Gub2IwUx1hqFr6bR0s="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/highcontrast.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/highcontrast.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3289265"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JXZXyLLbRcVs6jsSvV9fOKAbiwvSacWfnyrysq+Bo0g=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JXZXyLLbRcVs6jsSvV9fOKAbiwvSacWfnyrysq+Bo0g="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2910228"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Dn+pqxC9cFCFokZZ7R4bLEpfbhRQiJJoowvi4UUl5Jk=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Dn+pqxC9cFCFokZZ7R4bLEpfbhRQiJJoowvi4UUl5Jk="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4012354"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"WCX4P3XMTLpColzk+dXexLdlPFma/brEzZNzCS8ZCwM=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WCX4P3XMTLpColzk+dXexLdlPFma/brEzZNzCS8ZCwM="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2862309"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hBogYg++hf1yfTkrEsBTvsRwuHXVWpNiJYs+L735Zqc=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hBogYg++hf1yfTkrEsBTvsRwuHXVWpNiJYs+L735Zqc="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3967145"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UwRKUBr6H6q4igYPr6dcgAhRfAaEljBxLHytdvyDgWw=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UwRKUBr6H6q4igYPr6dcgAhRfAaEljBxLHytdvyDgWw="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2911132"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kch1qVEg8i+BfzBFfeq/uqzW1Y6j00cFxFrZ0Nw5kFs=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kch1qVEg8i+BfzBFfeq/uqzW1Y6j00cFxFrZ0Nw5kFs="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4068602"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kvipM7eTIB+iVS49eVvl/1d1kQkGDekS5yRlE+LbO3c=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kvipM7eTIB+iVS49eVvl/1d1kQkGDekS5yRlE+LbO3c="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2912441"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dbgFb57rXFhkq2FQt1cJsET2y3EQb0LDWAVlAy/bLpA=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dbgFb57rXFhkq2FQt1cJsET2y3EQb0LDWAVlAy/bLpA="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4069857"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zIqYpKntmNexovsonrcqd6rP77Vq6jG6rykiMOtsxyk=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zIqYpKntmNexovsonrcqd6rP77Vq6jG6rykiMOtsxyk="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2552042"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tanfjf4bGiFGnclcgSsNAXdbjGYCmDkqM8TixiGeKfo=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tanfjf4bGiFGnclcgSsNAXdbjGYCmDkqM8TixiGeKfo="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3369252"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1q+sYmhfpFf/CNqVwmsO+JoLI/7Pro0WllBV54MvlJg=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1q+sYmhfpFf/CNqVwmsO+JoLI/7Pro0WllBV54MvlJg="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2519754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ey6gTt/+H/s40uZswxTZN/k1YJc8SXLLnN2KZJS9kXg=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ey6gTt/+H/s40uZswxTZN/k1YJc8SXLLnN2KZJS9kXg="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3336934"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1DDYB+X+0hj2WzXoQJ7/B8IFPJaL0g6CjiJm/JR3fXw=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 00:59:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1DDYB+X+0hj2WzXoQJ7/B8IFPJaL0g6CjiJm/JR3fXw="}]}, {"Route": "css/bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/open-iconic/FONT-LICENSE", "AssetFile": "css/open-iconic/FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}]}, {"Route": "css/open-iconic/FONT-LICENSE.48tmkg660f", "AssetFile": "css/open-iconic/FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48tmkg660f"}, {"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}, {"Name": "label", "Value": "css/open-iconic/FONT-LICENSE"}]}, {"Route": "css/open-iconic/ICON-LICENSE", "AssetFile": "css/open-iconic/ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}]}, {"Route": "css/open-iconic/ICON-LICENSE.4dwjve0o0b", "AssetFile": "css/open-iconic/ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4dwjve0o0b"}, {"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}, {"Name": "label", "Value": "css/open-iconic/ICON-LICENSE"}]}, {"Route": "css/open-iconic/README.8h4oiah9s0.md", "AssetFile": "css/open-iconic/README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3658"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8h4oiah9s0"}, {"Name": "integrity", "Value": "sha256-rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM="}, {"Name": "label", "Value": "css/open-iconic/README.md"}]}, {"Route": "css/open-iconic/README.md", "AssetFile": "css/open-iconic/README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3658"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.0uw8dim9nl.eot", "AssetFile": "css/open-iconic/font/fonts/open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0uw8dim9nl"}, {"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.eot"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.eot", "AssetFile": "css/open-iconic/font/fonts/open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.h4d0pazwgy.woff", "AssetFile": "css/open-iconic/font/fonts/open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h4d0pazwgy"}, {"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.woff"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ll5grcv8wv.ttf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ll5grcv8wv"}, {"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.ttf"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.sjnzgf7e1h.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjnzgf7e1h"}, {"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ttf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.woff", "AssetFile": "css/open-iconic/font/fonts/open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}]}, {"Route": "css/site.1fpfb6x0uo.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2810"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1fpfb6x0uo"}, {"Name": "integrity", "Value": "sha256-bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2810"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Oct 2024 09:15:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}]}